using UnityEngine;
using UnityEngine.UI; 
using TMPro;
using System.Collections.Generic;
using System.Linq; 

public class LeaderboardUI : MonoBehaviour
{
    #region 比赛结果条目结构体
    public struct PlayerRankEntry
    {
        public int Rank;
        public string PlayerName;
        public float TotalTime;
        public bool IsPlayer; 

        public PlayerRankEntry(int _rank, string _name, float _time, bool _isPlayer = false)
        {
            Rank = _rank;
            PlayerName = _name;
            TotalTime = _time;
            IsPlayer = _isPlayer;
        }
    }
    #endregion

    #region 私有字段
    [Header("UI引用")]
    [Tooltip("ScrollView的Content Transform，用于放置排行榜条目")]
    [SerializeField] private Transform m_ScrollViewContent;
    [Tooltip("排行榜条目的预制件 (应挂载RankItemUI脚本)")]
    [SerializeField] private GameObject m_RankItemPrefab;
    [Tooltip("返回主菜单按钮")]
    [SerializeField] private Button m_BackButton;
    [Tooltip("继续驾驶按钮")]
    [SerializeField] private Button m_ContinueDrivingButton;
    [Tooltip("显示总金币的TextMeshProUGUI")]
    [SerializeField] private TextMeshProUGUI m_TotalCoinsText;
    [Tooltip("显示本局获得金币的TextMeshProUGUI")]
    [SerializeField] private TextMeshProUGUI m_EarnedCoinsText;

    [Header("数据源引用")]
    [Tooltip("对玩家比赛状态的引用 (用于获取玩家成绩)")]
    [SerializeField] private PlayerRaceState m_PlayerRaceState;
    private CarController m_PlayerCarController;

    [Header("排行榜配置")]
    [Tooltip("模拟AI玩家的数量")]
    [SerializeField] private int m_SimulatedAICount = 7;
    [Tooltip("AI玩家名称前缀")]
    [SerializeField] private string m_AINamePrefix = "AI Racer ";
    [Tooltip("AI玩家时间的最小浮动范围 (相对于玩家时间, 秒)")]
    [SerializeField, Range(-30f, 5f)] private float m_AITimeMinOffset = -15f;
    [Tooltip("AI玩家时间的最大浮动范围 (相对于玩家时间, 秒)")]
    [SerializeField, Range(0f, 60f)] private float m_AITimeMaxOffset = 30f;

    [Header("金币奖励配置")]
    [SerializeField, Tooltip("对PlayerInventorySO资产的引用")]
    private PlayerInventorySO m_PlayerInventorySO;
    [SerializeField, Tooltip("第一名奖励")] private int m_FirstPlaceBonus = 200;
    [SerializeField, Tooltip("第二名奖励")] private int m_SecondPlaceBonus = 100;
    [SerializeField, Tooltip("第三名奖励")] private int m_ThirdPlaceBonus = 50;
    [SerializeField, Tooltip("时间奖励的基础值")] private float m_BaseTimeReward = 120f;
    [SerializeField, Tooltip("每秒的时间惩罚率")] private float m_TimePenaltyRate = 1.0f;

    private List<RankItemUI> m_InstantiatedRankItems = new List<RankItemUI>();
    private bool m_CoinsAwardedThisRace = false; // 防止重复奖励的标志
    private int m_LastRaceReward = 0; // ：存储本局获得的金币
    #endregion

    #region Unity生命周期
    private void Awake()
    {
        Debug.Log($"[LeaderboardUI] Awake 被调用 - TimeSinceLevelLoad: {Time.timeSinceLevelLoad}s");
        
        if (m_ScrollViewContent == null) Debug.LogError("LeaderboardUI: m_ScrollViewContent 未分配!", this);
        if (m_RankItemPrefab == null) Debug.LogError("LeaderboardUI: m_RankItemPrefab 未分配!", this);
        if (m_RankItemPrefab != null && m_RankItemPrefab.GetComponent<RankItemUI>() == null)
        {
            Debug.LogError("LeaderboardUI: m_RankItemPrefab 上缺少 RankItemUI 组件!", this);
        }
        
        // 确保Content有垂直布局组件
        // EnsureVerticalLayoutOnContent(); // 移到Start，因为Awake中RectTransform可能未完全初始化
    }

    private void Start()
    {
        Debug.Log($"[LeaderboardUI] Start 被调用 - TimeSinceLevelLoad: {Time.timeSinceLevelLoad}s, GameObject激活状态: {gameObject.activeSelf}");
        
        // 移除对 PlayerRaceState 和 CarController 的主动查找。
        // 这些将依赖于外部设置或在 PlayerRaceState 被设置后获取。

        EnsureVerticalLayoutOnContent(); // 确保布局组件在此设置
        SetupButtonListeners();
    }

    private void OnEnable()
    {
        m_CoinsAwardedThisRace = false; 
        m_LastRaceReward = 0;

        if (m_TotalCoinsText != null) m_TotalCoinsText.text = "总金币: -";
        if (m_EarnedCoinsText != null) m_EarnedCoinsText.text = "本局获得: -";

        Debug.Log($"[LeaderboardUI] OnEnable 被调用 - TimeSinceLevelLoad: {Time.timeSinceLevelLoad}s, GameObject激活状态: {gameObject.activeSelf}");
        
        // 不再主动查找 PlayerRaceState。
        // 如果 PlayerRaceState 已通过 SetPlayerRaceStateAndRefresh 设置，或者有模拟AI，则填充。
        if (m_PlayerRaceState != null || m_SimulatedAICount > 0) 
        {
             Debug.Log($"[LeaderboardUI] OnEnable - 条件满足 (PlayerRaceState {(m_PlayerRaceState != null ? "存在" : "不存在")}, AI数量 {m_SimulatedAICount})，填充排行榜。");
             PopulateLeaderboard();
        }
        else
        {
            Debug.LogWarning("[LeaderboardUI] OnEnable - PlayerRaceState 仍然为 null，且无模拟AI，跳过填充排行榜。等待外部设置引用。", this);
        }
    }
    
    // 提取按钮监听器设置到单独的方法，避免代码重复
    private void SetupButtonListeners()
    {
        if (m_BackButton != null)
        {
            m_BackButton.onClick.RemoveAllListeners();
            m_BackButton.onClick.AddListener(OnBackToMainMenuPressed);
        }
        if (m_ContinueDrivingButton != null)
        {
            m_ContinueDrivingButton.onClick.RemoveAllListeners();
            m_ContinueDrivingButton.onClick.AddListener(OnContinueDrivingPressed);
        }
    }

    // 方法：确保Content有垂直布局组件
    private void EnsureVerticalLayoutOnContent()
    {
        if (m_ScrollViewContent == null) return;
        
        UnityEngine.UI.VerticalLayoutGroup verticalLayout = m_ScrollViewContent.GetComponent<UnityEngine.UI.VerticalLayoutGroup>();
        if (verticalLayout == null)
        {
            Debug.LogWarning("LeaderboardUI: ScrollViewContent缺少VerticalLayoutGroup组件，正在自动添加一个默认的。请在Inspector中根据需要调整其参数。", this.gameObject);
            verticalLayout = m_ScrollViewContent.gameObject.AddComponent<UnityEngine.UI.VerticalLayoutGroup>();
            // 不再修改verticalLayout的spacing, padding, childAlignment等属性，让用户在Inspector中设置
        }
        
        
        RectTransform contentRect = m_ScrollViewContent as RectTransform;
        if (contentRect != null)
        {
            contentRect.anchorMin = new Vector2(0, 1);    // 顶部左边
            contentRect.anchorMax = new Vector2(1, 1);    // 顶部右边
            contentRect.pivot = new Vector2(0.5f, 1);   // 轴心在顶部中心
            // 不再修改sizeDelta.y，让ContentSizeFitter或用户设置决定
            contentRect.sizeDelta = new Vector2(0, contentRect.sizeDelta.y); 
        }
        
        UnityEngine.UI.ContentSizeFitter sizeFitter = m_ScrollViewContent.GetComponent<UnityEngine.UI.ContentSizeFitter>();
        if (sizeFitter == null)
        {
            Debug.LogWarning("LeaderboardUI: ScrollViewContent缺少ContentSizeFitter组件，正在自动添加一个默认的。请在Inspector中根据需要调整其参数。", this.gameObject);
            sizeFitter = m_ScrollViewContent.gameObject.AddComponent<UnityEngine.UI.ContentSizeFitter>();
            // 设置默认的FitMode，用户可以在Inspector中更改
            sizeFitter.horizontalFit = UnityEngine.UI.ContentSizeFitter.FitMode.Unconstrained;
            sizeFitter.verticalFit = UnityEngine.UI.ContentSizeFitter.FitMode.PreferredSize; 
        }
    }
    #endregion

    #region 公共方法
    public void SetPlayerRaceStateAndRefresh(PlayerRaceState _playerRaceState)
    {
        if (_playerRaceState == null)
        {
            Debug.LogWarning("[LeaderboardUI] SetPlayerRaceStateAndRefresh 接收到 null PlayerRaceState。", this);
            m_PlayerRaceState = null;
            m_PlayerCarController = null;
            // 即使玩家状态为空，如果UI激活且有模拟AI，也可能需要刷新以显示模拟数据
            if (gameObject.activeInHierarchy && this.enabled && m_SimulatedAICount > 0)
            {
                Debug.Log("[LeaderboardUI] SetPlayerRaceStateAndRefresh - PlayerRaceState为null，但有模拟AI，尝试刷新排行榜。");
                PopulateLeaderboard();
            }
            return;
        }

        m_PlayerRaceState = _playerRaceState;
        m_PlayerCarController = null; // 重置以便重新获取

        // 尝试从 PlayerRaceState 或其父级获取 CarController
        m_PlayerCarController = m_PlayerRaceState.GetComponentInParent<CarController>();

        if (m_PlayerCarController == null)
        {
            // 备用查找：如果 PlayerRaceState 的根对象是玩家车辆 (通常带有 "Player" 标签)
            GameObject playerRoot = m_PlayerRaceState.transform.root.gameObject;
            if (playerRoot != null) // 确保根对象存在
            {
                m_PlayerCarController = playerRoot.GetComponent<CarController>();
                if (m_PlayerCarController == null)
                {
                     // 有些情况下，CarController可能不在根对象，而在PlayerRaceState所在的同一层级或其子级（不常见但可能）
                     m_PlayerCarController = m_PlayerRaceState.GetComponent<CarController>();
                }
            }
        }

        if (m_PlayerCarController == null)
        {
            Debug.LogWarning("[LeaderboardUI] SetPlayerRaceStateAndRefresh - 未能从 PlayerRaceState 及其层级结构中找到 CarController。", this);
        }
        else
        {
             Debug.Log("[LeaderboardUI] SetPlayerRaceStateAndRefresh - CarController 已找到/更新。", this);
        }

        Debug.Log($"[LeaderboardUI] PlayerRaceState 已通过 SetPlayerRaceStateAndRefresh 设置。LeaderboardUI激活状态: {gameObject.activeInHierarchy}, enabled: {this.enabled}");
        
        // 如果 LeaderboardUI 当前是激活并启用的，则刷新排行榜
        if (gameObject.activeInHierarchy && this.enabled)
        {
            Debug.Log("[LeaderboardUI] SetPlayerRaceStateAndRefresh - LeaderboardUI 激活，调用 PopulateLeaderboard。", this);
            PopulateLeaderboard();
        }
        else
        {
            Debug.Log("[LeaderboardUI] SetPlayerRaceStateAndRefresh - LeaderboardUI 未激活，PopulateLeaderboard 将在下次 OnEnable 时（或如果已Enable但未激活，则在激活时）根据当前状态填充。", this);
        }
    }

    public void PopulateLeaderboard()
    {
        if (m_ScrollViewContent == null || m_RankItemPrefab == null) return;

        // 确保布局组件存在
        EnsureVerticalLayoutOnContent();

        // 清理现有项目
        foreach (RankItemUI item in m_InstantiatedRankItems)
        {
            if (item != null) Destroy(item.gameObject);
        }
        m_InstantiatedRankItems.Clear();

        // 确保销毁后布局重新计算
        LayoutRebuilder.ForceRebuildLayoutImmediate(m_ScrollViewContent as RectTransform);

        List<PlayerRankEntry> raceResults = new List<PlayerRankEntry>();

        float playerTotalTime = 0f;
        bool playerResultValid = false;

        if (m_PlayerRaceState != null)
        {
            playerTotalTime = m_PlayerRaceState.TotalRaceTime;
            if (playerTotalTime > 0.001f) // 确保有有效时间, 避免浮点数精度问题
            {
                string playerName = "玩家"; // 默认名称
                // 从 PlayerInventorySO 获取玩家昵称
                if (m_PlayerInventorySO != null && !string.IsNullOrEmpty(m_PlayerInventorySO.PlayerName))
                {
                    playerName = m_PlayerInventorySO.PlayerName;
                }
                else if (m_PlayerRaceState.GetComponentInParent<CarController>()?.playerInventory != null && 
                         !string.IsNullOrEmpty(m_PlayerRaceState.GetComponentInParent<CarController>().playerInventory.PlayerName))
                {
                    // 尝试从 CarController 关联的 PlayerInventorySO 获取 (如果 LeaderboardUI 的 m_PlayerInventorySO 未直接设置)
                    playerName = m_PlayerRaceState.GetComponentInParent<CarController>().playerInventory.PlayerName;
                }

                raceResults.Add(new PlayerRankEntry(0, playerName, playerTotalTime, true));
                playerResultValid = true;
            }
            else
            {
                Debug.LogWarning("LeaderboardUI: 玩家比赛时间无效或未完成比赛。将使用模拟时间。", this);
            }
        }
        else
        {
            Debug.LogError("LeaderboardUI: PlayerRaceState 未引用，无法获取玩家成绩。将使用模拟时间。", this);
        }
        
        // 如果玩家数据无效，给一个基础时间用于AI模拟
        if (!playerResultValid) playerTotalTime = Random.Range(120f, 240f); 

        for (int i = 0; i < m_SimulatedAICount; i++)
        {
            string aiName = m_AINamePrefix + (i + 1).ToString();
            float aiTime = playerTotalTime + Random.Range(m_AITimeMinOffset, m_AITimeMaxOffset) + Random.Range(-5f, 5f) * ( (i % 3) -1 ); // 增加一点系统性随机
            aiTime = Mathf.Max(playerTotalTime * 0.8f, aiTime); // AI不会比玩家快太多
            aiTime = Mathf.Max(60f, aiTime); // AI成绩至少1分钟，除非玩家更快
            raceResults.Add(new PlayerRankEntry(0, aiName, aiTime, false));
        }

        List<PlayerRankEntry> sortedResults = raceResults.OrderBy(entry => entry.TotalTime).ToList();

        for (int i = 0; i < sortedResults.Count; i++)
        {
            GameObject itemGO = Instantiate(m_RankItemPrefab, m_ScrollViewContent);
            RankItemUI rankItemUI = itemGO.GetComponent<RankItemUI>();
            
            if (rankItemUI != null)
            {
                PlayerRankEntry currentEntry = sortedResults[i];
                rankItemUI.Setup(i + 1, currentEntry.PlayerName, currentEntry.TotalTime);
                m_InstantiatedRankItems.Add(rankItemUI);
                
                // 高亮玩家的排名项目并计算奖励
                if (currentEntry.IsPlayer)
                {
                    HighlightPlayerEntry(itemGO);

                    // --- 金币奖励计算 --- 
                    if (!m_CoinsAwardedThisRace)
                    { 
                        if (m_PlayerInventorySO != null)
                        {
                            int playerRank = i + 1;
                            int rankBonus = 0;
                            if (playerRank == 1) rankBonus = m_FirstPlaceBonus;
                            else if (playerRank == 2) rankBonus = m_SecondPlaceBonus;
                            else if (playerRank == 3) rankBonus = m_ThirdPlaceBonus;

                            float timeBonus = Mathf.Max(0f, m_BaseTimeReward - currentEntry.TotalTime * m_TimePenaltyRate);
                            int totalReward = rankBonus + Mathf.FloorToInt(timeBonus);

                            m_LastRaceReward = totalReward; // 存储本次奖励

                            if (totalReward > 0)
                            {
                                m_PlayerInventorySO.AddCoins(totalReward);
                                Debug.Log($"[LeaderboardUI] 比赛结算：玩家排名 {playerRank} (奖励 {rankBonus}), 时间 {currentEntry.TotalTime:F2}s (奖励 {Mathf.FloorToInt(timeBonus)})。总奖励: {totalReward} 金币。", this);
                            }
                            else
                            {
                                Debug.Log($"[LeaderboardUI] 比赛结算：玩家排名 {playerRank} (奖励 {rankBonus}), 时间 {currentEntry.TotalTime:F2}s (奖励 {Mathf.FloorToInt(timeBonus)})。总奖励为0或负数，不发放金币。", this);
                            }
                            m_CoinsAwardedThisRace = true; // 标记已奖励

                            // 更新金币显示文本
                            UpdateCoinDisplayTexts();
                        }
                        else
                        {
                            Debug.LogError("[LeaderboardUI] PlayerInventorySO 未在Inspector中分配，无法发放金币奖励！", this);
                        }
                    }
                    // --- 金币奖励计算结束 --- 
                }
            }
        }
        
        // 强制重新计算布局
        Canvas.ForceUpdateCanvases();
        LayoutRebuilder.ForceRebuildLayoutImmediate(m_ScrollViewContent as RectTransform);
    }
    #endregion

    #region 私有回调方法
    private void OnBackToMainMenuPressed()
    {
        Debug.Log("LeaderboardUI: 返回主菜单按钮被按下");
        
        Time.timeScale = 1f;

        if (MainMenuUIManager.Instance != null)
        {
            if(InGameUIManager.Instance != null) InGameUIManager.Instance.HideAllInGamePanels(); // 确保WinPanel被隐藏
            MainMenuUIManager.Instance.ShowMainMenuPanel(); 
        }
        else
        {
            Debug.LogWarning("LeaderboardUI: MainMenuUIManager 实例未找到！将直接加载主菜单场景。");
            // 作为备用，直接尝试加载主菜单场景 (假设场景名为 "MainMenu")
            UnityEngine.SceneManagement.SceneManager.LoadScene("MainMenu");
        }
    }

    private void OnContinueDrivingPressed()
    {
        Debug.Log("LeaderboardUI: 继续驾驶按钮被按下");

        Time.timeScale = 1f;

        if (m_PlayerCarController != null)
        {
            m_PlayerCarController.SetInputDisabled(false);
        }
        else
        {
            Debug.LogWarning("LeaderboardUI: CarController引用丢失，无法重新启用玩家输入。", this);
        }

        // ：通知 InGameUIManager 隐藏包括 WinPanel 在内的所有游戏内面板
        if (InGameUIManager.Instance != null)
        {
            InGameUIManager.Instance.HideAllInGamePanels();
        }
        else
        {
            Debug.LogWarning("LeaderboardUI: InGameUIManager 实例未找到，无法隐藏胜利面板！", this);
            
            if (panelToHide.parent != null && panelToHide.parent.name.ToLower().Contains("winpanel"))
            {
                // 如果父对象是WinPanel，则隐藏父对象
                panelToHide = panelToHide.parent;
            }
            panelToHide.gameObject.SetActive(false); 
            Debug.LogWarning("LeaderboardUI: Fallback - Attempted to hide " + panelToHide.name, this);
        }
    }

    private GameObject GetPanelToHide()
    {
        if (this.gameObject.name.ToLower().Contains("winpanel") || (transform.parent != null && transform.parent.name.ToLower().Contains("winpanel")))
        {
            return this.transform.parent != null && this.transform.parent.name.ToLower().Contains("winpanel") ? this.transform.parent.gameObject : this.gameObject;
        }
        Debug.LogWarning("LeaderboardUI.GetPanelToHide() is likely deprecated. Panel visibility should be managed by UIManager.");
        return gameObject; // Fallback, but usage should be reviewed.
    }

    // ：提取高亮逻辑
    private void HighlightPlayerEntry(GameObject _playerItemGO)
    {
        // 获取背景图像组件
        Image backgroundImage = _playerItemGO.GetComponent<Image>();
        if (backgroundImage != null)
        { 
            // 设置不同的颜色以突出显示玩家 (橙色)
            backgroundImage.color = new Color(1f, 0.647f, 0f, 0.65f); // 半透明橙色 (R=255, G=165, B=0)
        }
                    
        // 获取所有文本组件以设置不同的颜色
        TextMeshProUGUI[] texts = _playerItemGO.GetComponentsInChildren<TextMeshProUGUI>();
        foreach (TextMeshProUGUI text in texts)
        {
            text.color = Color.black; // 设置为黑色以便在金色背景上更清晰
            text.fontStyle = FontStyles.Bold; // 设置为粗体
        }
    }

    // ：更新金币显示文本的方法
    private void UpdateCoinDisplayTexts()
    {
        if (m_PlayerInventorySO != null)
        {
            if (m_TotalCoinsText != null)
            { 
                m_TotalCoinsText.text = $" {m_PlayerInventorySO.PlayerCoins}";
            }
            else
            { 
                Debug.LogWarning("[LeaderboardUI] m_TotalCoinsText 未在Inspector中分配。", this);
            }

            if (m_EarnedCoinsText != null)
            {
                m_EarnedCoinsText.text = $" +{m_LastRaceReward}";
            }
            else
            {
                 Debug.LogWarning("[LeaderboardUI] m_EarnedCoinsText 未在Inspector中分配。", this);
            }
        }
        else
        {
            // 如果库存数据都没有，则显示错误或默认值
             if (m_TotalCoinsText != null) m_TotalCoinsText.text = "总金币: N/A";
             if (m_EarnedCoinsText != null) m_EarnedCoinsText.text = "本局获得: N/A";
        }
    }
    #endregion
} 